# core/strategy.py

import pandas as pd

# 定义状态常量，便于维护和阅读
OPTIMISTIC_BULL = '乐观看涨'
CAUTIOUS_BULL = '谨慎看涨'
CAUTIOUS_BEAR = '谨慎看跌'
OPTIMISTIC_BEAR = '乐观看跌'

def calculate_mas(df: pd.DataFrame, windows: list[int]) -> pd.DataFrame:
    """为给定的DataFrame计算多个移动平均线。"""
    for window in windows:
        df[f'ma_{window}'] = df['close'].rolling(window=window, min_periods=window).mean()
    return df

def find_four_regime_periods(df: pd.DataFrame, fast_ma: int, slow_ma: int) -> dict:
    """
    根据新的四状态模型识别宏观状态区间。

    - 乐观看涨 (Optimistic Bull): F > S and S > S-1
    - 谨慎看涨 (Cautious Bull):  F < S and S > S-1
    - 谨慎看跌 (Cautious Bear):  F > S and S < S-1
    - 乐观看跌 (Optimistic Bear):  F < S and S < S-1

    Returns:
        一个字典，key为状态名称，value为(开始时间, 结束时间)的元组列表。
    """
    df = df.copy()
    if f'ma_{fast_ma}' not in df.columns or f'ma_{slow_ma}' not in df.columns:
        df = calculate_mas(df, [fast_ma, slow_ma])

    f_col = f'ma_{fast_ma}'
    s_col = f'ma_{slow_ma}'

    df['s_prev'] = df[s_col].shift(1)

    # 创建布尔条件
    f_gt_s = df[f_col] > df[s_col]
    s_rising = df[s_col] > df['s_prev']
    
    # 分配状态
    # 使用 np.select 更高效和清晰
    import numpy as np
    conditions = [
        f_gt_s & s_rising,
        ~f_gt_s & s_rising,
        f_gt_s & ~s_rising,
        ~f_gt_s & ~s_rising
    ]
    choices = [OPTIMISTIC_BULL, CAUTIOUS_BULL, CAUTIOUS_BEAR, OPTIMISTIC_BEAR]
    df['regime'] = np.select(conditions, choices, default=pd.NA)
    
    # 找出每个状态的连续区间
    df['regime_change'] = df['regime'].ne(df['regime'].shift())
    change_indices = df[df['regime_change']].index
    
    periods = {
        OPTIMISTIC_BULL: [],
        CAUTIOUS_BULL: [],
        CAUTIOUS_BEAR: [],
        OPTIMISTIC_BEAR: []
    }
    
    for i in range(len(change_indices) - 1):
        start_time = change_indices[i]
        end_time = change_indices[i+1]
        regime_type = df.loc[start_time, 'regime']
        if pd.notna(regime_type):
            periods[regime_type].append((start_time, end_time))

    # 处理最后一个区间
    last_start = change_indices[-1]
    last_regime = df.loc[last_start, 'regime']
    if pd.notna(last_regime):
        periods[last_regime].append((last_start, df.index[-1]))

    return periods