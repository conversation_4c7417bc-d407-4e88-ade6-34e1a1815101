# ui/main_window.py

import pandas as pd
import pyqtgraph as pg
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QLineEdit, QFileDialog, QMessageBox,
    QComboBox, QInputDialog, QFrame
)
from PySide6.QtGui import QColor, QFont
from PySide6.QtCore import Qt

from core.strategy import calculate_mas, find_four_regime_periods, \
    OPTIMISTIC_BULL, CAUTIOUS_BULL, CAUTIOUS_BEAR, OPTIMISTIC_BEAR
from ui.components import CandlestickItem

pg.setConfigOption('background', 'w')
pg.setConfigOption('foreground', 'k')

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("高级量化策略分析平台 v2.0")
        self.setGeometry(100, 100, 1800, 1000)

        # 数据存储
        self.dataframes = {} # key: timeframe_label, value: dataframe
        self.current_chart_df = None
        self.analysis_df = None
        self.regime_periods = {}
        self.regime_regions = []

        # 颜色定义
        self.regime_colors = {
            OPTIMISTIC_BULL: QColor(0, 255, 0, 40),   # 乐观绿
            CAUTIOUS_BULL: QColor(173, 255, 47, 40), # 谨慎绿 (黄绿)
            CAUTIOUS_BEAR: QColor(255, 165, 0, 40), # 谨慎红 (橙色)
            OPTIMISTIC_BEAR: QColor(255, 0, 0, 40),   # 乐观红
        }

        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # --- 控制面板 ---
        controls_panel = QFrame()
        controls_panel.setFrameShape(QFrame.StyledPanel)
        controls_layout = QHBoxLayout(controls_panel)

        # 数据加载
        self.btn_load_data = QPushButton("加载数据")
        controls_layout.addWidget(self.btn_load_data)
        
        # 周期选择
        controls_layout.addWidget(QLabel("分析周期:"))
        self.combo_analysis_tf = QComboBox()
        controls_layout.addWidget(self.combo_analysis_tf)
        
        controls_layout.addWidget(QLabel("图表周期:"))
        self.combo_chart_tf = QComboBox()
        controls_layout.addWidget(self.combo_chart_tf)

        # 分析参数
        controls_layout.addSpacing(20)
        controls_layout.addWidget(QLabel("分析均线 (快/慢):"))
        self.le_fast_ma = QLineEdit("20")
        self.le_slow_ma = QLineEdit("50")
        self.le_fast_ma.setFixedWidth(50)
        self.le_slow_ma.setFixedWidth(50)
        controls_layout.addWidget(self.le_fast_ma)
        controls_layout.addWidget(self.le_slow_ma)
        
        self.btn_run_analysis = QPushButton("运行分析")
        self.btn_run_analysis.setStyleSheet("QPushButton {background-color: #4CAF50; color: white;}")
        controls_layout.addWidget(self.btn_run_analysis)
        controls_layout.addStretch()

        main_layout.addWidget(controls_panel)

        # --- 图表 ---
        self.plot_widget = pg.PlotWidget(axisItems={'bottom': pg.DateAxisItem()})
        main_layout.addWidget(self.plot_widget)
        self.plot_item = self.plot_widget.getPlotItem()
        self.plot_item.showGrid(x=True, y=True, alpha=0.3)

        # --- 十字光标和信息显示 ---
        self.crosshair_v = pg.InfiniteLine(angle=90, movable=False, pen=pg.mkPen('k', style=Qt.DashLine))
        self.crosshair_h = pg.InfiniteLine(angle=0, movable=False, pen=pg.mkPen('k', style=Qt.DashLine))
        self.plot_item.addItem(self.crosshair_v, ignoreBounds=True)
        self.plot_item.addItem(self.crosshair_h, ignoreBounds=True)
        
        self.info_label = pg.TextItem(anchor=(0, 1)) # 左上角
        self.info_label.setPos(0, 0)
        self.plot_item.addItem(self.info_label)

    def connect_signals(self):
        self.btn_load_data.clicked.connect(self.load_data)
        self.combo_chart_tf.currentTextChanged.connect(self.switch_chart_timeframe)
        self.btn_run_analysis.clicked.connect(self.run_analysis)
        
        self.proxy = pg.SignalProxy(self.plot_item.scene().sigMouseMoved, rateLimit=60, slot=self.update_crosshair)

    def load_data(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择数据文件", "", "CSV Files (*.csv)")
        if not file_path:
            return

        label, ok = QInputDialog.getText(self, "输入周期", "为该数据文件指定一个标签 (例如: 1H, 4H, 1D):")
        if not ok or not label:
            return

        try:
            df = pd.read_csv(file_path, index_col=0, parse_dates=True).sort_index()
            # 确保列名是小写的标准格式
            df.columns = [x.lower() for x in df.columns]
            self.dataframes[label] = df
            
            # 更新下拉框
            self.combo_analysis_tf.addItem(label)
            self.combo_chart_tf.addItem(label)
            
            # 如果是第一个加载的数据，自动显示
            if len(self.dataframes) == 1:
                self.combo_chart_tf.setCurrentText(label)
                self.switch_chart_timeframe(label)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载或解析文件失败: {e}")

    def switch_chart_timeframe(self, label):
        if not label or label not in self.dataframes:
            return
        self.current_chart_df = self.dataframes[label]
        self.draw_chart()

    def run_analysis(self):
        analysis_label = self.combo_analysis_tf.currentText()
        if not analysis_label:
            QMessageBox.warning(self, "警告", "请选择一个分析周期。")
            return
        
        self.analysis_df = self.dataframes[analysis_label]

        try:
            fast_ma = int(self.le_fast_ma.text())
            slow_ma = int(self.le_slow_ma.text())
        except ValueError:
            QMessageBox.warning(self, "警告", "均线周期必须是整数。")
            return
            
        self.regime_periods = find_four_regime_periods(self.analysis_df, fast_ma, slow_ma)
        self.draw_chart() # 分析完成后重绘图表以显示新的区间
        QMessageBox.information(self, "完成", "分析完成，区间已在图表上更新。")

    def draw_chart(self):
        if self.current_chart_df is None:
            return
        
        self.plot_item.clear()
        
        # 1. 绘制背景区间
        self.regime_regions.clear()
        for regime, periods in self.regime_periods.items():
            color = self.regime_colors[regime]
            for start, end in periods:
                region = pg.LinearRegionItem(
                    values=[start.timestamp(), end.timestamp()],
                    orientation=pg.LinearRegionItem.Vertical,
                    brush=color, movable=False, pen=pg.mkPen(color)
                )
                self.plot_item.addItem(region)
                self.regime_regions.append(region)

        # 2. 绘制K线
        df = self.current_chart_df
        candle_data = [
            {'t': rec.Index.timestamp(), 'o': rec.open, 'h': rec.high, 'l': rec.low, 'c': rec.close}
            for rec in df.itertuples()
        ]
        self.candlestick_item = CandlestickItem(candle_data)
        self.plot_item.addItem(self.candlestick_item)

        # 3. 重新添加十字光标和标签
        self.plot_item.addItem(self.crosshair_v, ignoreBounds=True)
        self.plot_item.addItem(self.crosshair_h, ignoreBounds=True)
        self.plot_item.addItem(self.info_label)

    def update_crosshair(self, event):
        if self.current_chart_df is None or not self.plot_item.sceneBoundingRect().contains(event[0]):
            return
            
        mouse_point = self.plot_item.vb.mapSceneToView(event[0])
        x, y = mouse_point.x(), mouse_point.y()
        self.crosshair_v.setPos(x)
        self.crosshair_h.setPos(y)

        # 将x坐标（时间戳）转换为最接近的K线索引
        try:
            # 使用pandas的高效索引
            time_index = pd.to_datetime(x, unit='s')
            idx = self.current_chart_df.index.get_indexer([time_index], method='nearest')[0]
            candle_data = self.current_chart_df.iloc[idx]
        except (IndexError, KeyError):
            return

        # 更新信息标签
        date_str = candle_data.name.strftime('%Y-%m-%d %H:%M:%S')
        html = f"""
        <div style="background-color: rgba(230, 230, 230, 0.7); border: 1px solid black; padding: 5px;">
            <b style="font-size: 14px;">{date_str}</b><br>
            <span style="color: #00008B;">Open:</span> {candle_data.open:.2f}<br>
            <span style="color: #006400;">High:</span> {candle_data.high:.2f}<br>
            <span style="color: #8B0000;">Low:</span>  {candle_data.low:.2f}<br>
            <span style="color: #483D8B;">Close:</span> {candle_data.close:.2f}<br>
            <span style="color: #2F4F4F;">Volume:</span> {candle_data.volume:.0f}
        </div>
        """
        self.info_label.setHtml(html)
        # 调整标签位置，使其始终在视图内
        view_range = self.plot_item.getViewBox().viewRange()
        self.info_label.setPos(view_range[0][0], view_range[1][1])