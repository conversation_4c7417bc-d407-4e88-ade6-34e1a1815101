module QtQuick.VirtualKeyboard.Styles
linktarget Qt6::qtvkbstylesplugin
plugin qtvkbstylesplugin
classname QtQuickVirtualKeyboardStylesPlugin
typeinfo plugins.qmltypes
import QtQuick.VirtualKeyboard.Styles.Builtin auto
depends QtQuick auto
depends QtQuick
prefer :/qt-project.org/imports/QtQuick/VirtualKeyboard/Styles/
KeyboardStyle 6.0 KeyboardStyle.qml
KeyboardStyle 2.0 KeyboardStyle.qml
KeyboardStyle 1.0 KeyboardStyle.qml
KeyIcon 6.0 KeyIcon.qml
KeyIcon 2.0 KeyIcon.qml
KeyIcon 1.0 KeyIcon.qml
KeyPanel 6.0 KeyPanel.qml
KeyPanel 2.0 KeyPanel.qml
KeyPanel 1.0 KeyPanel.qml
SelectionListItem 6.0 SelectionListItem.qml
SelectionListItem 2.0 SelectionListItem.qml
SelectionListItem 1.0 SelectionListItem.qml
TraceInputKeyPanel 6.0 TraceInputKeyPanel.qml
TraceInputKeyPanel 2.0 TraceInputKeyPanel.qml
TraceInputKeyPanel 1.0 TraceInputKeyPanel.qml
TraceCanvas 6.0 TraceCanvas.qml
TraceCanvas 2.0 TraceCanvas.qml
TraceCanvas 1.0 TraceCanvas.qml
TraceUtils 6.0 TraceUtils.js
TraceUtils 2.0 TraceUtils.js
TraceUtils 1.0 TraceUtils.js

