# ui/components.py

import pyqtgraph as pg
from PySide6.QtGui import QPicture, QPainter
from PySide6.QtCore import QRectF

class CandlestickItem(pg.GraphicsObject):
    def __init__(self, data):
        """
        data must be a list of dicts, each with keys:
        't', 'o', 'h', 'l', 'c' (time, open, high, low, close)
        """
        pg.GraphicsObject.__init__(self)
        self.data = data
        self.generatePicture()

    def generatePicture(self):
        self.picture = QPicture()
        p = QPainter(self.picture)
        # 默认K线宽度为时间间隔的80%
        w = (self.data[1]['t'] - self.data[0]['t']) * 0.8
        for candle in self.data:
            t, o, h, l, c = candle.values()
            # 绘制影线
            p.setPen(pg.mkPen('k'))
            p.drawLine(pg.PointF(t, l), pg.PointF(t, h))
            # 绘制实体
            if o > c: # 下跌
                p.setBrush(pg.mkBrush('r'))
            else: # 上涨
                p.setBrush(pg.mkBrush('g'))
            p.drawRect(QRectF(t - w / 2, o, w, c - o))
        p.end()

    def paint(self, p, *args):
        p.drawPicture(0, 0, self.picture)

    def boundingRect(self):
        return QRectF(self.picture.boundingRect())